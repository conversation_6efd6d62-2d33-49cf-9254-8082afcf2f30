"""
Langfuse Utility Class

A simple utility class for initializing Langfuse, creating generations, and ending generations.
Inspired by the provided example code for structured data extraction.
"""

from typing import Optional, Dict, Any, List
import logging
import socket
import platform
import os
import getpass
import requests
import threading
import time
from datetime import datetime

from langfuse import Langfuse
from app.core.configuration import settings

logger = logging.getLogger(__name__)


class LangfuseUtil:
    """
    Simple utility class for Langfuse operations including initialization,
    generation creation, and generation ending.
    """

    def __init__(self):
        """Initialize Langfuse client with settings from configuration."""
        self.langfuse = None
        self._initialize_langfuse()

    def _initialize_langfuse(self) -> None:
        """Initialize Langfuse client using settings from configuration."""
        try:
            if not settings.LANGFUSE_ENABLED:
                logger.info("<PERSON><PERSON> is disabled in settings")
                return

            if not all([settings.LANGFUSE_SECRET_KEY, settings.LANGFUSE_PUBLIC_KEY, settings.LANGFUSE_HOST]):
                logger.warning("Langfuse configuration incomplete - missing required keys")
                return

            self.langfuse = Langfuse(
                secret_key=settings.LANGFUSE_SECRET_KEY,
                public_key=settings.LANGFUSE_PUBLIC_KEY,
                host=settings.LANGFUSE_HOST,
                flush_at=settings.LANGFUSE_FLUSH_AT,
                flush_interval=settings.LANGFUSE_FLUSH_INTERVAL
            )

            logger.info("Langfuse client initialized successfully")
            return

        except Exception as e:
            logger.error(f"Failed to initialize Langfuse: {str(e)}")
            self.langfuse = None

    @property
    def is_enabled(self) -> bool:
        """Check if Langfuse is enabled and properly initialized."""
        return self.langfuse is not None

    def _get_user_metadata(self) -> Dict[str, Any]:
        """
        Collect metadata about the user's environment for tracking purposes.

        Returns:
            Dictionary containing user environment metadata
        """
        metadata = {}

        try:
            # Basic system information
            metadata["hostname"] = socket.gethostname()
            metadata["platform"] = platform.platform()
            metadata["system"] = platform.system()
            metadata["machine"] = platform.machine()
            metadata["processor"] = platform.processor()
            metadata["python_version"] = platform.python_version()

            # User information
            metadata["username"] = getpass.getuser()

            # Environment variables (selective)
            metadata["environment"] = settings.ENVIRONMENT if hasattr(settings, 'ENVIRONMENT') else 'unknown'

            # Working directory
            metadata["working_directory"] = os.getcwd()

            # Timestamp
            metadata["session_start"] = datetime.now().isoformat()

        except Exception as e:
            logger.warning(f"Failed to collect some metadata: {str(e)}")

        try:
            # Try to get local IP address
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                # Connect to a remote address (doesn't actually send data)
                s.connect(("*******", 80))
                metadata["local_ip"] = s.getsockname()[0]
        except Exception as e:
            logger.debug(f"Could not determine local IP: {str(e)}")
            metadata["local_ip"] = "unknown"

        try:
            # Try to get public IP address (with timeout)
            response = requests.get("https://api.ipify.org?format=json", timeout=3)
            if response.status_code == 200:
                metadata["public_ip"] = response.json().get("ip", "unknown")
            else:
                metadata["public_ip"] = "unknown"
        except Exception as e:
            logger.debug(f"Could not determine public IP: {str(e)}")
            metadata["public_ip"] = "unknown"

        # Add application-specific metadata
        metadata["application"] = settings.PROJECT_NAME if hasattr(settings, 'PROJECT_NAME') else 'document-data-extraction'
        metadata["aws_region"] = settings.AWS_REGION if hasattr(settings, 'AWS_REGION') else 'unknown'

        return metadata

    def create_generation(
        self,
        name: str,
        model: str,
        system_prompt: Optional[str] = None,
        user_prompt: Optional[str] = None,
        input_messages: Optional[List[Dict[str, str]]] = None,
        model_parameters: Optional[Dict[str, Any]] = None,
        include_user_metadata: bool = True
    ) -> Optional[Any]:
        """
        Create a standalone generation without trace context.

        Args:
            name: Name of the generation (e.g., "generation_extraction")
            model: Model identifier (e.g., "amazon.nova-pro-v1:0")
            system_prompt: System prompt content
            user_prompt: User prompt content
            input_messages: Pre-formatted input messages (alternative to system/user prompts)
            model_parameters: Model parameters like temperature, max_tokens, top_p
            include_user_metadata: Whether to include user environment metadata (default: True)

        Returns:
            Generation object if successful, None otherwise
        """
        if not self.is_enabled:
            logger.warning("Cannot create generation - Langfuse not enabled")
            return None

        try:
            # Prepare input messages
            if input_messages:
                messages = input_messages
            else:
                messages = []
                if system_prompt:
                    messages.append({"role": "system", "content": system_prompt})
                if user_prompt:
                    messages.append({"role": "user", "content": user_prompt})

            # Default model parameters
            default_params = {
                "temperature": 0.1,
                "max_tokens": 4000,
                "top_p": 0.9
            }

            # Merge with provided parameters
            final_params = {**default_params, **(model_parameters or {})}

            # Collect user metadata if enabled
            metadata = {}
            if include_user_metadata:
                metadata = self._get_user_metadata()
                logger.debug(f"Including user metadata: hostname={metadata.get('hostname')}, "
                           f"username={metadata.get('username')}, "
                           f"local_ip={metadata.get('local_ip')}")

            # Create generation with metadata
            generation = self.langfuse.generation(
                name=name,
                model=model,
                input=messages,
                model_parameters=final_params,
                metadata=metadata
            )

            logger.debug(f"Created generation: {name} with metadata")
            return generation

        except Exception as e:
            logger.error(f"Failed to create generation '{name}': {str(e)}")
            return None

    def create_trace(
        self,
        name: str,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        include_user_metadata: bool = True,
        additional_metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[Any]:
        """
        Create a trace with user metadata for comprehensive tracking.

        Args:
            name: Name of the trace
            user_id: Optional user identifier
            session_id: Optional session identifier
            include_user_metadata: Whether to include user environment metadata (default: True)
            additional_metadata: Additional metadata to include

        Returns:
            Trace object if successful, None otherwise
        """
        if not self.is_enabled:
            logger.warning("Cannot create trace - Langfuse not enabled")
            return None

        try:
            # Collect metadata
            metadata = additional_metadata or {}

            if include_user_metadata:
                user_metadata = self._get_user_metadata()
                metadata.update(user_metadata)

            # Use hostname as user_id if not provided
            if not user_id and include_user_metadata:
                user_id = f"{metadata.get('username', 'unknown')}@{metadata.get('hostname', 'unknown')}"

            # Create trace
            trace = self.langfuse.trace(
                name=name,
                user_id=user_id,
                session_id=session_id,
                metadata=metadata
            )

            logger.debug(f"Created trace: {name} for user: {user_id}")
            return trace

        except Exception as e:
            logger.error(f"Failed to create trace '{name}': {str(e)}")
            return None

    def end_generation(
        self,
        generation: Any,
        output: str,
        usage_details: Optional[Dict[str, int]] = None,
        level: str = "DEFAULT",
        status_message: Optional[str] = None
    ) -> None:
        """
        End a generation with output and usage details.

        Args:
            generation: The generation object to end
            output: The output from the model
            usage_details: Token usage details with keys: input, output, total
            level: Level for the generation (DEFAULT, ERROR, etc.)
            status_message: Optional status message for errors
        """
        if generation is None:
            logger.warning("Cannot end generation - generation object is None")
            return

        try:
            end_params: Dict[str, Any] = {"output": output}

            # Add usage details if provided
            if usage_details:
                end_params["usage_details"] = usage_details

            # Add level and status message for errors
            if level != "DEFAULT":
                end_params["level"] = level

            if status_message:
                end_params["status_message"] = status_message

            generation.end(**end_params)
            logger.debug("Generation ended successfully")

        except Exception as e:
            logger.error(f"Failed to end generation: {str(e)}")

    def flush(self) -> None:
        """Ensure data is flushed to Langfuse."""
        if not self.is_enabled:
            return

        try:
            self.langfuse.flush()
            logger.debug("Langfuse data flushed")
            return

        except Exception as e:
            logger.error(f"Failed to flush Langfuse data: {str(e)}")

    def shutdown(self) -> None:
        """Properly shutdown Langfuse client and clean up resources with timeout."""
        if not self.is_enabled or self.langfuse is None:
            return

        def _flush_with_timeout():
            """Helper function to flush with timeout."""
            try:
                if self.langfuse is not None:
                    self.langfuse.flush()
                    logger.info("Langfuse data flushed successfully")
            except Exception as e:
                logger.error(f"Error during Langfuse flush: {str(e)}")

        try:
            logger.info("Flushing Langfuse data before shutdown...")

            # Create a thread for flushing with timeout
            flush_thread = threading.Thread(target=_flush_with_timeout)
            flush_thread.daemon = True  # Make it a daemon thread
            flush_thread.start()

            # Wait for flush to complete with timeout
            flush_thread.join(timeout=2.0)  # 2 second timeout

            if flush_thread.is_alive():
                logger.warning("Langfuse flush timed out after 2 seconds, proceeding with shutdown")

            # Set to None to allow garbage collection to handle cleanup
            self.langfuse = None

        except Exception as e:
            logger.error(f"Error during Langfuse shutdown: {str(e)}")
            # Force cleanup even if there's an error
            self.langfuse = None

    def __del__(self):
        """Destructor to ensure proper cleanup when object is garbage collected."""
        try:
            self.shutdown()
        except Exception:
            # Ignore errors during cleanup in destructor
            pass


def get_langfuse_util() -> LangfuseUtil:
    """Create a new instance of LangfuseUtil (no longer singleton)."""
    return LangfuseUtil()